import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AttritionPrediction } from './entities/attrition-prediction.entity';
import { CompetencyFramework } from './entities/competency-framework.entity';
import { CareerPath } from './entities/career-path.entity';
import { AiInsight } from './entities/ai-insight.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AttritionPrediction,
      CompetencyFramework,
      CareerPath,
      AiInsight,
    ]),
  ],
  controllers: [],
  providers: [],
  exports: [TypeOrmModule],
})
export class AiModule {}
