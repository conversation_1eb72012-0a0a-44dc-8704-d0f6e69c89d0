import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  IconButton,
  Tooltip,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  TrendingUp as TrendingUpIcon,
  CalendarToday as CalendarIcon,
  Assessment as AssessmentIcon,
  Dashboard as DashboardIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { ApiService } from '../../services/api';
import { DashboardSummary } from './DashboardSummary';
import { KpiManagement } from './KpiManagement';
import { SubmissionForm } from './SubmissionForm';

interface DashboardSubmission {
  id: number;
  organizationalUnit: {
    id: number;
    name: string;
    type: string;
  };
  submissionMonth: number;
  submissionYear: number;
  status: 'draft' | 'submitted' | 'approved' | 'rejected';
  completionDate: string;
  overallStatus: 'green' | 'yellow' | 'red' | 'na';
}

interface DashboardStats {
  totalTeams: number;
  submittedTeams: number;
  onTimeSubmissions: number;
  submissionRate: number;
  onTimeRate: number;
  statusCounts: {
    green: number;
    yellow: number;
    red: number;
    na: number;
  };
}

const MonthlyDashboardsPage: React.FC = () => {
  const { user } = useAuth();
  const [submissions, setSubmissions] = useState<DashboardSubmission[]>([]);
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1);
  const [activeTab, setActiveTab] = useState(0);
  const [submissionFormOpen, setSubmissionFormOpen] = useState(false);
  const [editingSubmission, setEditingSubmission] = useState(null);

  useEffect(() => {
    fetchDashboardData();
  }, [selectedYear, selectedMonth]);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // 🔐 NIS2-COMPLIANT: Use centralized API service instead of direct fetch
      const [submissionsResponse, statsResponse] = await Promise.all([
        ApiService.getMonthlyDashboardSubmissions({
          year: selectedYear,
          month: selectedMonth
        }),
        ApiService.getMonthlyDashboardStatistics({
          year: selectedYear,
          month: selectedMonth
        })
      ]);

      // Validate responses
      if (!submissionsResponse || !statsResponse) {
        throw new Error('Failed to fetch dashboard data');
      }

      // Extract data from API responses
      const submissionsData = submissionsResponse.data || submissionsResponse;
      const statsData = statsResponse.data || statsResponse;

      setSubmissions(submissionsData.submissions || []);
      setStats(statsData);
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError('Failed to load dashboard data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'green': return 'success';
      case 'yellow': return 'warning';
      case 'red': return 'error';
      default: return 'default';
    }
  };

  const getSubmissionStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'success';
      case 'submitted': return 'info';
      case 'draft': return 'warning';
      case 'rejected': return 'error';
      default: return 'default';
    }
  };

  const formatMonth = (month: number, year: number) => {
    const date = new Date(year, month - 1);
    return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
  };

  const canCreateSubmission = () => {
    return user?.role === 'manager' || user?.role === 'director' || user?.role === 'hr_admin';
  };

  const canViewAllSubmissions = () => {
    return user?.role === 'ceo' || user?.role === 'director' || user?.role === 'hr_admin';
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const canManageKpis = () => {
    return user?.role === 'ceo' || user?.role === 'hr_admin';
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Monthly Dashboards
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Track team performance and KPIs across the organization
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange} aria-label="dashboard tabs">
          <Tab
            icon={<DashboardIcon />}
            label="Dashboard Summary"
            iconPosition="start"
          />
          <Tab
            icon={<AssessmentIcon />}
            label="Submissions"
            iconPosition="start"
          />
          {canManageKpis() && (
            <Tab
              icon={<SettingsIcon />}
              label="KPI Management"
              iconPosition="start"
            />
          )}
        </Tabs>
      </Box>

      {/* Tab Content */}
      {activeTab === 0 && <DashboardSummary />}

      {activeTab === 1 && (
        <Box>

          {/* Statistics Cards */}
          {stats && (
            <Grid container spacing={3} sx={{ mb: 4 }}>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent>
                    <Box display="flex" alignItems="center" justifyContent="space-between">
                      <Box>
                        <Typography color="text.secondary" gutterBottom>
                          Total Teams
                        </Typography>
                        <Typography variant="h4">
                          {stats.totalTeams || 0}
                        </Typography>
                      </Box>
                      <AssessmentIcon color="primary" sx={{ fontSize: 40 }} />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent>
                    <Box display="flex" alignItems="center" justifyContent="space-between">
                      <Box>
                        <Typography color="text.secondary" gutterBottom>
                          Submission Rate
                        </Typography>
                        <Typography variant="h4">
                          {stats.submissionRate?.toFixed(1) || '0'}%
                        </Typography>
                      </Box>
                      <TrendingUpIcon color="primary" sx={{ fontSize: 40 }} />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent>
                    <Box display="flex" alignItems="center" justifyContent="space-between">
                      <Box>
                        <Typography color="text.secondary" gutterBottom>
                          On-Time Rate
                        </Typography>
                        <Typography variant="h4">
                          {stats.onTimeRate?.toFixed(1) || '0'}%
                        </Typography>
                      </Box>
                      <CalendarIcon color="primary" sx={{ fontSize: 40 }} />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent>
                    <Typography color="text.secondary" gutterBottom>
                      Status Distribution
                    </Typography>
                    <Box display="flex" gap={1} flexWrap="wrap">
                      <Chip
                        label={`${stats.statusCounts?.green || 0} Green`}
                        color="success"
                        size="small"
                      />
                      <Chip
                        label={`${stats.statusCounts?.yellow || 0} Yellow`}
                        color="warning"
                        size="small"
                      />
                      <Chip
                        label={`${stats.statusCounts?.red || 0} Red`}
                        color="error"
                        size="small"
                      />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}

          {/* Action Buttons */}
          <Box sx={{ mb: 3, display: 'flex', gap: 2 }}>
            {canCreateSubmission() && (
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => {
                  setEditingSubmission(null);
                  setSubmissionFormOpen(true);
                }}
              >
                New Submission
              </Button>
            )}

            <Button
              variant="outlined"
              onClick={() => {
                // TODO: Open overview/trends view
                console.log('View overview');
              }}
            >
              View Overview
            </Button>
          </Box>

          {/* Submissions List */}
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              {formatMonth(selectedMonth, selectedYear)} Submissions
            </Typography>

            {submissions.length === 0 ? (
              <Box textAlign="center" py={4}>
                <Typography color="text.secondary">
                  No submissions found for {formatMonth(selectedMonth, selectedYear)}
                </Typography>
              </Box>
            ) : (
              <Grid container spacing={2}>
                {submissions.map((submission) => (
                  <Grid item xs={12} md={6} lg={4} key={submission.id}>
                    <Card variant="outlined">
                      <CardContent>
                        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                          <Typography variant="h6" component="h3">
                            {submission.organizationalUnit?.name || 'Unknown Team'}
                          </Typography>
                          <Chip
                            label={submission.status?.toUpperCase() || 'UNKNOWN'}
                            color={getSubmissionStatusColor(submission.status) as any}
                            size="small"
                          />
                        </Box>

                        <Typography color="text.secondary" gutterBottom>
                          {submission.organizationalUnit?.type || 'Unknown Type'}
                        </Typography>

                        <Box display="flex" justifyContent="space-between" alignItems="center" mt={2}>
                          <Chip
                            label={submission.status?.toUpperCase() || 'UNKNOWN'}
                            color={getSubmissionStatusColor(submission.status) as any}
                            size="small"
                          />
                          <Typography variant="body2" color="text.secondary">
                            {submission.completionDate ? new Date(submission.completionDate).toLocaleDateString() : 'No date'}
                          </Typography>
                        </Box>
                      </CardContent>

                      <CardActions>
                        <Tooltip title="View Details">
                          <IconButton
                            size="small"
                            onClick={() => {
                              // TODO: Open submission details
                              console.log('View submission:', submission.id);
                            }}
                          >
                            <ViewIcon />
                          </IconButton>
                        </Tooltip>

                        {(submission.status === 'draft' || submission.status === 'rejected') && (
                          <Tooltip title="Edit Submission">
                            <IconButton
                              size="small"
                              onClick={() => {
                                setEditingSubmission(submission);
                                setSubmissionFormOpen(true);
                              }}
                            >
                              <EditIcon />
                            </IconButton>
                          </Tooltip>
                        )}
                      </CardActions>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}
          </Paper>
        </Box>
      )}

      {/* KPI Management Tab */}
      {activeTab === 2 && canManageKpis() && <KpiManagement />}

      {/* Submission Form Dialog */}
      <SubmissionForm
        open={submissionFormOpen}
        onClose={() => {
          setSubmissionFormOpen(false);
          setEditingSubmission(null);
        }}
        onSuccess={() => {
          fetchDashboardData();
        }}
        editingSubmission={editingSubmission}
        month={selectedMonth}
        year={selectedYear}
      />
    </Box>
  );
};

export default MonthlyDashboardsPage;
