{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "noImplicitReturns": false, "noImplicitThis": false, "noUnusedLocals": false, "noUnusedParameters": false}, "include": ["src"], "exclude": ["node_modules", "build"]}