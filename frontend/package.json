{"name": "ehrx-frontend", "version": "1.0.0", "private": true, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "dependencies": {"@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@mui/icons-material": "^5.11.16", "@mui/material": "^5.12.1", "@mui/x-date-pickers": "^8.7.0", "ajv": "^8.12.0", "ajv-keywords": "^5.1.0", "axios": "^1.3.5", "chart.js": "^4.2.1", "cors": "^2.8.5", "date-fns": "^4.1.0", "express": "^5.1.0", "formik": "^2.2.9", "jwt-decode": "^3.1.2", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-dom": "^18.3.1", "react-router-dom": "^6.10.0", "react-scripts": "^5.0.1", "recharts": "^3.1.0", "web-vitals": "^2.1.4", "yup": "^1.1.1"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}